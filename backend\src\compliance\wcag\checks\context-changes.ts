/**
 * WCAG-064: Context Changes Check (3.2.5 Level AAA)
 * 70% Automated - Detects unexpected context changes
 */

import { Page } from 'puppeteer';
import { EnhancedCheckTemplate, EnhancedCheckConfig } from '../utils/enhanced-check-template';
import { AccessibilityPatternLibrary } from '../utils/accessibility-pattern-library';
import { ModernFrameworkOptimizer } from '../utils/modern-framework-optimizer';
import { AISemanticValidator } from '../utils/ai-semantic-validator';
import { WcagEvidence } from '../types';
import { WcagCheckResultEnhanced } from '../types-enhanced';
import { EvidenceStandardizer } from '../utils/evidence-standardizer';
import SmartCache from '../utils/smart-cache';

interface ContextChangeDetection {
  unexpectedChanges: number;
  userControlledChanges: number;
  automaticChanges: number;
  warningMechanisms: number;
  changeTypes: string[];
  riskLevel: 'none' | 'low' | 'medium' | 'high';
  detectionConfidence: number;
}

interface UserControlMechanismValidation {
  hasUserControl: boolean;
  hasWarningSystem: boolean;
  hasConfirmationDialogs: boolean;
  hasDisableOption: boolean;
  controlTypes: string[];
  adequateControl: boolean;
}

interface UnexpectedChangeAnalysis {
  autoRedirects: number;
  focusChanges: number;
  formAutoSubmissions: number;
  selectAutoNavigation: number;
  popupWindows: number;
  unexpectedScore: number;
  riskAssessment: 'low' | 'medium' | 'high';
}

export interface ContextChangesConfig extends EnhancedCheckConfig {
  enableContextChangeDetection?: boolean;
  enableUserControlValidation?: boolean;
  enableUnexpectedChangeAnalysis?: boolean;
  enableAccessibilityControlTesting?: boolean;
  enableAccessibilityPatterns?: boolean;
  enableModernFrameworkOptimization?: boolean;
  enableComponentLibraryDetection?: boolean;
}

export class ContextChangesCheck {
  private enhancedTemplate = new EnhancedCheckTemplate();
  private smartCache = SmartCache.getInstance();
  private accessibilityPatternLibrary = AccessibilityPatternLibrary.getInstance();
  private modernFrameworkOptimizer = ModernFrameworkOptimizer.getInstance();
  private aiSemanticValidator = AISemanticValidator.getAIInstance();

  async performCheck(config: ContextChangesConfig): Promise<WcagCheckResultEnhanced> {
    // Enhanced configuration with specialized context change detection
    const enhancedConfig: ContextChangesConfig = {
      ...config,
      enableUtilityIntegration: true,
      utilityConfig: {
        enablePatternValidation: true,
        enableCaching: true,
        enableGracefulFallback: true,
        integrationStrategy: 'supplement',
        maxExecutionTime: 2000, // Target: <2s performance
      },
      enableContextChangeDetection: true,
      enableUserControlValidation: true,
      enableUnexpectedChangeAnalysis: true,
      enableAccessibilityControlTesting: true,
      enableAccessibilityPatterns: true,
      enableModernFrameworkOptimization: true,
      enableComponentLibraryDetection: true,
    };

    const result = await this.enhancedTemplate.executeEnhancedCheck(
      'WCAG-064',
      'Change on Request',
      'predictable',
      0.0305,
      'AAA',
      enhancedConfig,
      this.executeContextChangesCheck.bind(this),
      true, // Requires browser
      false, // No manual review
    );

    // Enhanced evidence standardization with context change analysis
    const enhancedEvidence = await EvidenceStandardizer.standardizeEvidenceEnhanced(
      result.evidence,
      {
        ruleId: 'WCAG-064',
        ruleName: 'Change on Request',
        scanDuration: result.executionTime,
        elementsAnalyzed: result.evidence.length,
        checkSpecificData: {
          automationRate: 0.7,
          checkType: 'context-change-analysis',
          behaviorAnalysis: true,
          userInitiatedChanges: true,
          accessibilityPatterns: enhancedConfig.enableAccessibilityPatterns,
          componentLibraryDetection: enhancedConfig.enableComponentLibraryDetection,
        },
      },
      {
        enableAdvancedSelectors: true,
        enableContextAnalysis: true,
        enablePerformanceOptimization: true,
        evidenceQualityThreshold: 0.7,
        maxEvidenceItems: 20,
      }
    );

    return {
      ...result,
      evidence: enhancedEvidence,
    };
  }

  private async executeContextChangesCheck(
    page: Page,
    _config: ContextChangesConfig,
  ): Promise<{
    score: number;
    maxScore: number;
    evidence: WcagEvidence[];
    issues: string[];
    recommendations: string[];
  }> {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];

    // Specialized Context Change Detection Algorithm - Advanced Implementation
    const contextChangeDetection = await this.executeContextChangeDetection(page);

    // User Control Mechanism Validation Algorithm
    const userControlValidation = await this.validateUserControlMechanisms(page);

    // Unexpected Change Analysis Algorithm
    const unexpectedChangeAnalysis = await this.analyzeUnexpectedChanges(page);

    // Accessibility Control Testing Algorithm
    const accessibilityControlTesting = await this.testAccessibilityControls(page);

    // Combine all specialized detection results
    const allAnalyses = [
      contextChangeDetection,
      userControlValidation,
      unexpectedChangeAnalysis,
      accessibilityControlTesting,
    ];

    let totalChecks = 0;
    let passedChecks = 0;

    allAnalyses.forEach((analysis) => {
      totalChecks += analysis.totalChecks;
      passedChecks += analysis.passedChecks;
      evidence.push(...analysis.evidence);
      issues.push(...analysis.issues);
      recommendations.push(...analysis.recommendations);
    });

    // Calculate score with 80% accuracy target
    const score = totalChecks > 0 ? Math.round((passedChecks / totalChecks) * 100) : 100;

    return {
      score,
      maxScore: 100,
      evidence,
      issues,
      recommendations,
    };
  }

  /**
   * Context Change Detection Algorithm - Core Implementation
   * Target: 80% context change validation accuracy
   */
  private async executeContextChangeDetection(page: Page) {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];

    const contextChangeDetection = await page.evaluate((): ContextChangeDetection => {
      // Advanced context change detection
      const changeElements = document.querySelectorAll(
        'meta[http-equiv="refresh"], [onchange*="location"], [onchange*="submit"], [onclick*="window"], [target="_blank"], [target="_new"]'
      );

      let unexpectedChanges = 0;
      let userControlledChanges = 0;
      let automaticChanges = 0;
      let warningMechanisms = 0;
      const changeTypes: string[] = [];

      // Analyze meta refresh
      const metaRefresh = document.querySelectorAll('meta[http-equiv="refresh"]');
      metaRefresh.forEach(meta => {
        const content = meta.getAttribute('content') || '';
        const hasUrl = content.includes('url=');
        if (hasUrl) {
          automaticChanges++;
          changeTypes.push('meta-refresh');
        }
      });

      // Analyze form elements with onchange navigation
      const autoNavigateSelects = document.querySelectorAll('select[onchange]');
      autoNavigateSelects.forEach(select => {
        const onchange = select.getAttribute('onchange') || '';
        if (onchange.includes('location') || onchange.includes('submit') || onchange.includes('navigate')) {
          automaticChanges++;
          changeTypes.push('select-auto-navigate');
        }
      });

      // Analyze links that open new windows
      const newWindowLinks = document.querySelectorAll('a[target="_blank"], a[target="_new"]');
      newWindowLinks.forEach(link => {
        const hasWarning = link.textContent?.includes('new window') ||
                          link.textContent?.includes('opens in') ||
                          link.getAttribute('aria-label')?.includes('new window') ||
                          link.getAttribute('title')?.includes('new window');

        if (hasWarning) {
          userControlledChanges++;
          warningMechanisms++;
        } else {
          unexpectedChanges++;
        }
        changeTypes.push('new-window');
      });

      // Analyze JavaScript navigation
      const jsNavigationElements = document.querySelectorAll('[onclick*="location"], [onclick*="window"]');
      jsNavigationElements.forEach(element => {
        const onclick = element.getAttribute('onclick') || '';
        const hasConfirmation = onclick.includes('confirm') || onclick.includes('alert');

        if (hasConfirmation) {
          userControlledChanges++;
          warningMechanisms++;
        } else {
          unexpectedChanges++;
        }
        changeTypes.push('js-navigation');
      });

      // Analyze focus-triggered changes
      const focusChangeElements = document.querySelectorAll('[onfocus*="location"], [onblur*="location"]');
      focusChangeElements.forEach(() => {
        unexpectedChanges++;
        changeTypes.push('focus-change');
      });

      // Calculate risk level
      const totalChanges = unexpectedChanges + userControlledChanges + automaticChanges;
      const unexpectedRatio = totalChanges > 0 ? unexpectedChanges / totalChanges : 0;

      let riskLevel: ContextChangeDetection['riskLevel'] = 'none';
      let detectionConfidence = 0.8;

      if (totalChanges === 0) {
        riskLevel = 'none';
        detectionConfidence = 0.9;
      } else if (unexpectedRatio <= 0.2) {
        riskLevel = 'low';
        detectionConfidence = 0.85;
      } else if (unexpectedRatio <= 0.5) {
        riskLevel = 'medium';
        detectionConfidence = 0.8;
      } else {
        riskLevel = 'high';
        detectionConfidence = 0.75;
      }

      return {
        unexpectedChanges,
        userControlledChanges,
        automaticChanges,
        warningMechanisms,
        changeTypes: [...new Set(changeTypes)],
        riskLevel,
        detectionConfidence,
      };
    });

    const totalChecks = 1;
    let passedChecks = 0;

    if (contextChangeDetection.riskLevel === 'none' || contextChangeDetection.riskLevel === 'low') {
      passedChecks = 1;
      evidence.push({
        type: 'text',
        description: 'Context change detection: Low or no unexpected context changes',
        value: `Risk level: ${contextChangeDetection.riskLevel}, Confidence: ${(contextChangeDetection.detectionConfidence * 100).toFixed(1)}%`,
        severity: 'info',
      });
    } else {
      issues.push(`Context change risk: ${contextChangeDetection.riskLevel} risk detected`);
      evidence.push({
        type: 'code',
        description: `Context change detection: ${contextChangeDetection.riskLevel} risk`,
        value: `Unexpected: ${contextChangeDetection.unexpectedChanges}, User-controlled: ${contextChangeDetection.userControlledChanges}, Automatic: ${contextChangeDetection.automaticChanges}, Warnings: ${contextChangeDetection.warningMechanisms}`,
        severity: contextChangeDetection.riskLevel === 'high' ? 'error' : 'warning',
      });
      recommendations.push('Add user control and warnings for context changes');
    }

    // Report detected change types
    if (contextChangeDetection.changeTypes.length > 0) {
      evidence.push({
        type: 'text',
        description: 'Detected context change types',
        value: contextChangeDetection.changeTypes.join(', '),
        severity: 'info',
      });
    }

    return {
      totalChecks,
      passedChecks,
      evidence,
      issues,
      recommendations,
    };
  }

  /**
   * User Control Mechanism Validation Algorithm
   */
  private async validateUserControlMechanisms(page: Page) {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];

    const userControlValidation = await page.evaluate((): UserControlMechanismValidation => {
      // Check for user control mechanisms
      const confirmationDialogs = document.querySelectorAll(
        '[onclick*="confirm"], [onclick*="alert"], .confirm-dialog, .confirmation'
      );

      const warningElements = document.querySelectorAll(
        '.warning, .notice, [data-warning], [aria-label*="warning"], [title*="warning"]'
      );

      const disableOptions = document.querySelectorAll(
        '.disable-redirect, .no-redirect, [data-disable-redirect], .stop-auto'
      );

      const userControlElements = document.querySelectorAll(
        '.user-control, .manual-control, [data-user-control], .opt-out'
      );

      const hasUserControl = userControlElements.length > 0 || confirmationDialogs.length > 0;
      const hasWarningSystem = warningElements.length > 0;
      const hasConfirmationDialogs = confirmationDialogs.length > 0;
      const hasDisableOption = disableOptions.length > 0;

      const controlTypes: string[] = [];
      if (hasConfirmationDialogs) controlTypes.push('confirmation');
      if (hasWarningSystem) controlTypes.push('warning');
      if (hasDisableOption) controlTypes.push('disable');
      if (hasUserControl) controlTypes.push('user-control');

      // Check for adequate control (at least 2 mechanisms)
      const adequateControl = controlTypes.length >= 2;

      return {
        hasUserControl,
        hasWarningSystem,
        hasConfirmationDialogs,
        hasDisableOption,
        controlTypes,
        adequateControl,
      };
    });

    const totalChecks = 1;
    let passedChecks = 0;

    if (userControlValidation.adequateControl) {
      passedChecks = 1;
      evidence.push({
        type: 'text',
        description: 'User control validation: Adequate control mechanisms available',
        value: `Control types: ${userControlValidation.controlTypes.join(', ')}`,
        severity: 'info',
      });
    } else {
      issues.push('Insufficient user control mechanisms for context changes');
      evidence.push({
        type: 'code',
        description: 'User control validation: Inadequate control mechanisms',
        value: `Available controls: ${userControlValidation.controlTypes.join(', ') || 'none'} (need at least 2)`,
        severity: 'error',
      });
      recommendations.push('Add user control mechanisms: confirmations, warnings, or disable options');
    }

    return {
      totalChecks,
      passedChecks,
      evidence,
      issues,
      recommendations,
    };
  }

  /**
   * Unexpected Change Analysis Algorithm
   */
  private async analyzeUnexpectedChanges(page: Page) {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];

    const unexpectedAnalysis = await page.evaluate((): UnexpectedChangeAnalysis => {
      // Count different types of unexpected changes
      let autoRedirects = 0;
      let focusChanges = 0;
      let formAutoSubmissions = 0;
      let selectAutoNavigation = 0;
      let popupWindows = 0;

      // Auto-redirects (meta refresh)
      const metaRefresh = document.querySelectorAll('meta[http-equiv="refresh"]');
      metaRefresh.forEach(meta => {
        const content = meta.getAttribute('content') || '';
        if (content.includes('url=')) {
          autoRedirects++;
        }
      });

      // Focus changes that trigger navigation
      const focusElements = document.querySelectorAll('[onfocus], [onblur]');
      focusElements.forEach(element => {
        const onfocus = element.getAttribute('onfocus') || '';
        const onblur = element.getAttribute('onblur') || '';
        if (onfocus.includes('location') || onblur.includes('location') ||
            onfocus.includes('window') || onblur.includes('window')) {
          focusChanges++;
        }
      });

      // Form auto-submissions
      const autoSubmitForms = document.querySelectorAll('form[onload], form[onchange]');
      autoSubmitForms.forEach(form => {
        const onload = form.getAttribute('onload') || '';
        const onchange = form.getAttribute('onchange') || '';
        if (onload.includes('submit') || onchange.includes('submit')) {
          formAutoSubmissions++;
        }
      });

      // Select auto-navigation
      const autoNavSelects = document.querySelectorAll('select[onchange]');
      autoNavSelects.forEach(select => {
        const onchange = select.getAttribute('onchange') || '';
        if (onchange.includes('location') || onchange.includes('navigate')) {
          selectAutoNavigation++;
        }
      });

      // Popup windows (JavaScript)
      const popupElements = document.querySelectorAll('[onclick*="window.open"], [onclick*="popup"]');
      popupWindows = popupElements.length;

      // Calculate unexpected score (0-1, where 1 is very unexpected)
      const totalUnexpected = autoRedirects + focusChanges + formAutoSubmissions + selectAutoNavigation + popupWindows;
      const unexpectedScore = Math.min(totalUnexpected / 10, 1); // Normalize to 0-1

      // Determine risk assessment
      let riskAssessment: UnexpectedChangeAnalysis['riskAssessment'] = 'low';
      if (unexpectedScore > 0.7) {
        riskAssessment = 'high';
      } else if (unexpectedScore > 0.3) {
        riskAssessment = 'medium';
      }

      return {
        autoRedirects,
        focusChanges,
        formAutoSubmissions,
        selectAutoNavigation,
        popupWindows,
        unexpectedScore,
        riskAssessment,
      };
    });

    const totalChecks = 1;
    let passedChecks = 0;

    if (unexpectedAnalysis.riskAssessment === 'low') {
      passedChecks = 1;
      evidence.push({
        type: 'text',
        description: 'Unexpected change analysis: Low risk of unexpected changes',
        value: `Risk assessment: ${unexpectedAnalysis.riskAssessment}, Score: ${(unexpectedAnalysis.unexpectedScore * 100).toFixed(1)}%`,
        severity: 'info',
      });
    } else {
      issues.push(`Unexpected change risk: ${unexpectedAnalysis.riskAssessment} risk detected`);
      evidence.push({
        type: 'code',
        description: `Unexpected change analysis: ${unexpectedAnalysis.riskAssessment} risk`,
        value: `Auto-redirects: ${unexpectedAnalysis.autoRedirects}, Focus changes: ${unexpectedAnalysis.focusChanges}, Auto-submissions: ${unexpectedAnalysis.formAutoSubmissions}, Select navigation: ${unexpectedAnalysis.selectAutoNavigation}, Popups: ${unexpectedAnalysis.popupWindows}`,
        severity: unexpectedAnalysis.riskAssessment === 'high' ? 'error' : 'warning',
      });
      recommendations.push('Reduce unexpected context changes and add user control mechanisms');
    }

    return {
      totalChecks,
      passedChecks,
      evidence,
      issues,
      recommendations,
    };
  }

  /**
   * Accessibility Control Testing Algorithm
   */
  private async testAccessibilityControls(page: Page) {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];

    const accessibilityControls = await page.$$eval(
      'button, [role="button"], input[type="button"], input[type="submit"]',
      (elements) => {
        return elements.map((element, index) => {
          const text = element.textContent?.toLowerCase() || '';
          const ariaLabel = element.getAttribute('aria-label')?.toLowerCase() || '';
          const title = element.getAttribute('title')?.toLowerCase() || '';
          const allText = `${text} ${ariaLabel} ${title}`;

          // Check for context change control keywords
          const isContextControl = ['confirm', 'cancel', 'continue', 'proceed', 'navigate', 'redirect', 'submit']
            .some(keyword => allText.includes(keyword));

          const hasKeyboardAccess = element.hasAttribute('tabindex') ||
                                   ['BUTTON', 'INPUT'].includes(element.tagName);

          const hasAriaLabel = element.hasAttribute('aria-label') ||
                              element.hasAttribute('aria-labelledby');

          const isAccessible = hasKeyboardAccess && (text.length > 0 || hasAriaLabel);

          return {
            index,
            selector: `${element.tagName.toLowerCase()}:nth-of-type(${index + 1})`,
            isContextControl,
            hasKeyboardAccess,
            hasAriaLabel,
            isAccessible,
            text: text.substring(0, 50), // Limit text length
          };
        });
      }
    );

    const contextControls = accessibilityControls.filter(control => control.isContextControl);
    const totalChecks = contextControls.length;
    let passedChecks = 0;

    contextControls.forEach((control, index) => {
      if (control.isAccessible) {
        passedChecks++;
        evidence.push({
          type: 'text',
          description: `Context control ${index + 1} is accessible`,
          value: `${control.selector} - "${control.text}" - keyboard accessible and properly labeled`,
          severity: 'info',
        });
      } else {
        issues.push(`Context control ${index + 1} lacks accessibility features`);
        evidence.push({
          type: 'code',
          description: `Context control ${index + 1} needs accessibility improvements`,
          value: `${control.selector} - keyboard: ${control.hasKeyboardAccess}, label: ${control.hasAriaLabel}`,
          severity: 'warning',
        });
        recommendations.push(`Improve accessibility of context control ${index + 1}`);
      }
    });

    // If no context controls found, that's generally good
    if (totalChecks === 0) {
      return {
        totalChecks: 1,
        passedChecks: 1,
        evidence: [{
          type: 'text',
          description: 'Accessibility control testing: No context change controls detected',
          value: 'No context change controls found to test',
          severity: 'info',
        }],
        issues: [],
        recommendations: [],
      };
    }

    return {
      totalChecks,
      passedChecks,
      evidence,
      issues,
      recommendations,
    };
  }



        // Check for automatic redirects
        if (element.tagName.toLowerCase() === 'meta') {
          const httpEquiv = element.getAttribute('http-equiv');
          const content = element.getAttribute('content');
          if (httpEquiv === 'refresh' && content) {
            const hasDelay = /^\d+/.test(content);
            const delay = hasDelay ? parseInt(content.match(/^\d+/)?.[0] || '0') : 0;
            
            issues.push({
              changeType: 'meta-refresh',
              hasWarning: false,
              hasUserControl: false,
              isAutomatic: true,
              triggerEvent: 'automatic',
              description: `Meta refresh with ${delay} second delay`,
              severity: delay < 5 ? 'error' : 'warning',
            });
          }
        }

        // Check for JavaScript that might cause context changes
        const onclickHandler = element.getAttribute('onclick');
        if (onclickHandler) {
          // Check for window operations
          if (/window\.(?:open|close|location|history)/i.test(onclickHandler)) {
            issues.push({
              changeType: 'javascript-navigation',
              hasWarning: false,
              hasUserControl: true,
              isAutomatic: false,
              triggerEvent: 'onclick',
              description: 'JavaScript navigation in onclick handler',
              severity: 'warning',
            });
          }

          // Check for form submission
          if (/submit|form/i.test(onclickHandler)) {
            issues.push({
              changeType: 'javascript-form-submit',
              hasWarning: false,
              hasUserControl: true,
              isAutomatic: false,
              triggerEvent: 'onclick',
              description: 'JavaScript form submission',
              severity: 'info',
            });
          }
        }

        // Check for automatic form submission
        if (element.tagName.toLowerCase() === 'form') {
          const hasAutoSubmit = element.hasAttribute('data-auto-submit') ||
                               element.querySelector('[onchange*="submit"], [onblur*="submit"]') !== null;
          
          if (hasAutoSubmit) {
            issues.push({
              changeType: 'auto-form-submit',
              hasWarning: false,
              hasUserControl: false,
              isAutomatic: true,
              triggerEvent: 'change/blur',
              description: 'Form with automatic submission',
              severity: 'error',
            });
          }
        }

        // Check for select elements that might auto-navigate
        if (element.tagName.toLowerCase() === 'select') {
          const onchangeHandler = element.getAttribute('onchange');
          if (onchangeHandler && /location|window|submit|navigate/i.test(onchangeHandler)) {
            issues.push({
              changeType: 'select-auto-navigate',
              hasWarning: false,
              hasUserControl: false,
              isAutomatic: true,
              triggerEvent: 'onchange',
              description: 'Select element with automatic navigation',
              severity: 'error',
            });
          }
        }

        // Check for links that open in new windows without warning
        if (element.tagName.toLowerCase() === 'a') {
          const target = element.getAttribute('target');
          const href = element.getAttribute('href');
          
          if (target === '_blank' && href) {
            const hasWarning = element.textContent?.includes('new window') ||
                              element.textContent?.includes('new tab') ||
                              element.hasAttribute('aria-label') && 
                              (element.getAttribute('aria-label') || '').includes('new window') ||
                              element.querySelector('[aria-hidden="false"]') !== null;
            
            issues.push({
              changeType: 'new-window-link',
              hasWarning,
              hasUserControl: true,
              isAutomatic: false,
              triggerEvent: 'click',
              description: hasWarning ? 'Link opens new window with warning' : 'Link opens new window without warning',
              severity: hasWarning ? 'info' : 'warning',
            });
          }

          // Check for JavaScript links
          if (href && href.startsWith('javascript:')) {
            issues.push({
              changeType: 'javascript-link',
              hasWarning: false,
              hasUserControl: true,
              isAutomatic: false,
              triggerEvent: 'click',
              description: 'JavaScript link that may cause context change',
              severity: 'warning',
            });
          }
        }

        // Check for elements with automatic focus changes
        const hasFocusEvents = element.hasAttribute('onfocus') || element.hasAttribute('onblur');
        if (hasFocusEvents) {
          const onfocusHandler = element.getAttribute('onfocus') || '';
          const onblurHandler = element.getAttribute('onblur') || '';
          
          if (/location|window|submit|navigate/i.test(onfocusHandler + onblurHandler)) {
            issues.push({
              changeType: 'focus-context-change',
              hasWarning: false,
              hasUserControl: false,
              isAutomatic: true,
              triggerEvent: 'focus/blur',
              description: 'Element causes context change on focus/blur',
              severity: 'error',
            });
          }
        }

        // Check for popup/modal triggers
        const hasPopupTrigger = element.hasAttribute('data-toggle') ||
                               element.hasAttribute('data-target') ||
                               element.classList.contains('modal-trigger') ||
                               element.classList.contains('popup-trigger');
        
        if (hasPopupTrigger) {
          const hasWarning = element.textContent?.includes('popup') ||
                            element.textContent?.includes('modal') ||
                            element.hasAttribute('aria-label') &&
                            (element.getAttribute('aria-label') || '').includes('popup');
          
          issues.push({
            changeType: 'popup-modal',
            hasWarning,
            hasUserControl: true,
            isAutomatic: false,
            triggerEvent: 'click',
            description: hasWarning ? 'Popup/modal trigger with warning' : 'Popup/modal trigger without warning',
            severity: hasWarning ? 'info' : 'warning',
          });
        }

        // Add issues to problematic elements
        issues.forEach(issue => {
          problematicElements.push({
            selector: generateSelector(element, index),
            tagName: element.tagName.toLowerCase(),
            changeType: issue.changeType,
            hasWarning: issue.hasWarning,
            hasUserControl: issue.hasUserControl,
            isAutomatic: issue.isAutomatic,
            triggerEvent: issue.triggerEvent,
            attributes: getRelevantAttributes(element),
            description: issue.description,
            severity: issue.severity,
          });
        });
      });

      return {
        problematicElements,
        totalElements: allElements.length,
      };

      function generateSelector(element: Element, index: number): string {
        if (element.id) return `#${element.id}`;
        if (element.className) {
          const classes = element.className.split(' ').filter(c => c.trim());
          if (classes.length > 0) return `.${classes[0]}`;
        }
        return `${element.tagName.toLowerCase()}:nth-of-type(${index + 1})`;
      }

      function getRelevantAttributes(element: Element): Record<string, string> {
        const attrs: Record<string, string> = {};
        const relevantAttrs = ['id', 'class', 'href', 'target', 'onclick', 'onchange', 'onfocus', 'onblur', 'data-toggle', 'data-target'];
        
        relevantAttrs.forEach(attr => {
          const value = element.getAttribute(attr);
          if (value) attrs[attr] = value.substring(0, 100); // Limit length
        });

        return attrs;
      }
    });

    const scanDuration = Date.now() - startTime;
    let score = 100;
    const elementCount = contextAnalysis.problematicElements.length;

    if (elementCount > 0) {
      // Categorize issues by severity
      const errorElements = contextAnalysis.problematicElements.filter(el => el.severity === 'error');
      const warningElements = contextAnalysis.problematicElements.filter(el => el.severity === 'warning');
      const infoElements = contextAnalysis.problematicElements.filter(el => el.severity === 'info');

      // Calculate score penalties
      if (errorElements.length > 0) {
        score -= Math.min(50, errorElements.length * 15);
        issues.push(`${errorElements.length} elements cause unexpected context changes`);
      }

      if (warningElements.length > 0) {
        score -= Math.min(30, warningElements.length * 8);
        issues.push(`${warningElements.length} elements may cause unexpected context changes`);
      }

      if (infoElements.length > 0) {
        issues.push(`${infoElements.length} elements cause context changes with appropriate warnings`);
      }

      evidence.push({
        type: 'interaction',
        description: 'Elements that may cause context changes',
        value: `Found ${elementCount} elements that may cause context changes`,
        elementCount,
        affectedSelectors: contextAnalysis.problematicElements.map(el => el.selector),
        severity: errorElements.length > 0 ? 'error' : warningElements.length > 0 ? 'warning' : 'info',
        fixExample: {
          before: '<select onchange="window.location=this.value">',
          after: '<select onchange="if(confirm(\'Navigate to \' + this.options[this.selectedIndex].text + \'?\')) window.location=this.value">',
          description: 'Provide user control and warnings for context changes',
          codeExample: `
<!-- Before: Unexpected context changes -->
<meta http-equiv="refresh" content="5;url=newpage.html">
<select onchange="window.location=this.value">
  <option value="page1.html">Page 1</option>
  <option value="page2.html">Page 2</option>
</select>
<a href="document.pdf" target="_blank">Download PDF</a>

<!-- After: User-controlled context changes -->
<p>This page will redirect in 5 seconds. <a href="newpage.html">Continue now</a> or <button onclick="clearTimeout(redirectTimer)">Cancel</button></p>

<label for="navigation-select">Choose a page to navigate to:</label>
<select id="navigation-select">
  <option value="">Select a page...</option>
  <option value="page1.html">Page 1</option>
  <option value="page2.html">Page 2</option>
</select>
<button onclick="navigateToSelected()">Go to Selected Page</button>

<a href="document.pdf" target="_blank">
  Download PDF <span aria-label="opens in new window">(new window)</span>
</a>
          `,
          resources: [
            'https://www.w3.org/WAI/WCAG21/Understanding/change-on-request.html',
            'https://www.w3.org/WAI/WCAG21/Techniques/general/G200',
            'https://www.w3.org/WAI/WCAG21/Techniques/general/G201'
          ]
        },
        metadata: {
          scanDuration,
          elementsAnalyzed: contextAnalysis.totalElements,
          checkSpecificData: {
            totalProblematicElements: elementCount,
            errorElements: errorElements.length,
            warningElements: warningElements.length,
            infoElements: infoElements.length,
            changeTypes: [...new Set(contextAnalysis.problematicElements.map(el => el.changeType))],
            automaticChanges: contextAnalysis.problematicElements.filter(el => el.isAutomatic).length,
            userControlledChanges: contextAnalysis.problematicElements.filter(el => el.hasUserControl).length,
          },
        },
      });

      // Add specific examples for problematic elements
      contextAnalysis.problematicElements.slice(0, 10).forEach(element => {
        evidence.push({
          type: 'interaction',
          description: `Context change: ${element.description}`,
          value: `<${element.tagName} ${Object.entries(element.attributes)
            .map(([key, value]) => `${key}="${value}"`)
            .join(' ')}>`,
          selector: element.selector,
          severity: element.severity,
          metadata: {
            checkSpecificData: {
              changeType: element.changeType,
              hasWarning: element.hasWarning,
              hasUserControl: element.hasUserControl,
              isAutomatic: element.isAutomatic,
              triggerEvent: element.triggerEvent,
            },
          },
        });
      });

      recommendations.push('Ensure context changes are initiated only by user request');
      recommendations.push('Provide clear warnings before context changes occur');
      recommendations.push('Allow users to control timing of automatic redirects');
      recommendations.push('Use confirmation dialogs for navigation triggered by form controls');
      
      if (errorElements.length > 0) {
        recommendations.push('CRITICAL: Remove automatic context changes that occur without user control');
      }
      
      if (warningElements.length > 0) {
        recommendations.push('Add warnings for links that open in new windows');
        recommendations.push('Provide user control for navigation triggered by form elements');
      }
    } else {
      // No problematic context changes found
      evidence.push({
        type: 'info',
        description: 'No unexpected context changes detected',
        value: 'Page does not contain elements that cause unexpected context changes',
        severity: 'info',
        metadata: {
          scanDuration,
          elementsAnalyzed: contextAnalysis.totalElements,
          checkSpecificData: {
            noContextChanges: true,
          },
        },
      });
    }

    // Ensure score doesn't go below 0
    score = Math.max(0, score);

    return {
      score,
      maxScore: 100,
      evidence,
      issues,
      recommendations,
    };
  }
}
